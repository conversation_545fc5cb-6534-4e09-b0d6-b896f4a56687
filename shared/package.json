{"name": "@flakiness/shared", "version": "0.157.0", "private": true, "description": "", "type": "module", "types": "./types/src", "main": "./lib", "keywords": [], "author": "Degu Labs, Inc", "license": "Fair Source 100", "scripts": {"test": "npx playwright test -c playwright.config.ts"}, "exports": {"./*": {"types": "./types/src/*", "import": "./lib/*", "require": "./lib/*"}}, "dependencies": {"debug": "^4.4.3", "stable-hash": "^0.0.6", "xxhash-wasm": "^1.1.0"}, "peerDependencies": {"zod": "^4.3.5"}, "devDependencies": {"@playwright/test": "^1.57.0", "@types/debug": "^4.1.12", "@types/express": "^4.17.20", "express": "^4.22.1", "zod": "^4.3.5"}}
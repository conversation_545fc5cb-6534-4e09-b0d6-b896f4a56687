import { Kysely } from 'kysely';

import * as baseTypes from '../generated/022_acceptable_flakiness_types.js';
// import * as newTypes from '../generated/022_acceptable_flakiness_types.js';
import { schema as baseSchema } from './022_acceptable_flakiness.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  pending_runs: {
    columns: {
      project_id: { ref: 'projects.project_id', onDelete: 'cascade', is: ['not-null'] },
      run_id: { type: 'integer', is: ['not-null'] },
      status: { type: 'integer', is: ['not-null'] },
    },
    uniques: {
      'project_id + run_id': {
        columns: ['project_id', 'run_id']
      },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<any>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}

import { Database } from "@flakiness/database";
import debug from "debug";
import path from "path";
import url from "url";
import { HTTPServer } from "../node/httpServer.js";
import { startWorkerService } from "../node/nodeutils.js";
import { Services } from "../node/services.js";
const log = debug("fk:app_process");
const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const serverConfig = await HTTPServer.configFromEnvOrDie();
const servicesConfig = await Services.configFromEnvOrDie();
{
  const result = await Database.migrate(servicesConfig.dbConfig, async (migrator) => {
    return await migrator.migrateToLatest();
  });
  if (result.error) {
    log("Failed to migrate database to latest revision!");
    log(result.error);
    process.exit(1);
  }
  if (result.results?.length) {
    log(`Successfully applied ${result.results.length} migrations!`);
  } else {
    log(`Database is latest version.`);
  }
}
const services = await Services.initializeOrDie(servicesConfig);
const builderWorker = startWorkerService(path.join(__dirname, "builder_worker.js"), {
  workerName: "nodejs worker",
  servicesConfig,
  telemetryConfig: serverConfig.telemetry ? {
    port: serverConfig.port + 1
  } : void 0
});
const server = await HTTPServer.create(services, serverConfig);
log(`Server started with pid = ${process.pid}`);
const teardown = (signal) => {
  log(`Server Received ${signal} - terminating.`);
  services.dispose();
  builderWorker.worker.postMessage("STOP");
  server.dispose();
};
services.license?.expiredPromise().then(() => teardown("license_expired"));
for (const signal of ["SIGTERM", "SIGINT"])
  process.on(signal, () => teardown(signal));
//# sourceMappingURL=app_process.js.map

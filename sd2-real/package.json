{"name": "@flakiness/server", "private": true, "version": "0.156.0", "description": "", "type": "module", "types": "./types/src", "main": "./lib", "keywords": [], "author": "", "license": "ISC", "scripts": {"test": "npx playwright test -c playwright.config.ts"}, "exports": {"./*": {"types": "./types/src/*", "import": "./lib/*", "require": "./lib/*"}}, "dependencies": {"@aws-sdk/client-s3": "3.460.0", "@aws-sdk/s3-request-presigner": "3.460.0", "@flakiness/database": "workspace:*", "@flakiness/sdk": "^0.154.0", "@flakiness/shared": "workspace:*", "@octokit/types": "^16.0.0", "body-parser": "^1.20.2", "chalk": "^5.3.0", "commander": "^12.1.0", "compression": "^1.8.1", "cookie-parser": "^1.4.6", "debug": "^4.4.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "jsonwebtoken": "^9.0.2", "ms": "^2.1.3", "octokit": "^5.0.5", "pg": "^8.16.0", "pg-format": "^1.0.4", "prom-client": "^15.0.0", "stripe": "^17.4.0", "supports-color": "^10.0.0", "temporal-polyfill": "^0.3.0", "vlq": "^2.0.4", "zod": "^4.3.5"}, "devDependencies": {"@octokit/core": "^7.0.6", "@octokit/plugin-rest-endpoint-methods": "^17.0.0", "@types/body-parser": "^1.19.6", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.5", "@types/debug": "^4.1.12", "@types/express": "^4.17.20", "@types/jsonwebtoken": "^9.0.10", "@types/ms": "^2.1.0", "@types/pg": "^8.15.2", "@types/pg-format": "^1.0.5", "@types/ws": "^8.18.1", "glob": "^10.3.10", "podkeeper": "^0.3.1"}}
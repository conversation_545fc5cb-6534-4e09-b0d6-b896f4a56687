## DevOps

- onboard more people using the Flakiness.io platform and reporter.

## Flakiness Report V1

- introduce versions harness to the repository.
- s/userSuppliedData/metadata
- s/systemUtilization/telemetry ??
- split telemetry into `cpuTelemetry` and `ramTelemetry`?
- drop snippets, add "sources" to the report?
- make sure telemetry is LESS FAT?
- Error snippets are NOT deprecated; some test runners actually report them.

## Flakiness SDK

- add readReport to the sdk?
- Move isInteractiveTTY check to the sdk?

## Reporters Ecosystem

- cleanup & publish mitata reporter
- most used Node.js test runners: https://2024.stateofjs.com/en-US/libraries/testing/
    * jest: 62%
    * storybook: 40%
    * vitest: 34%
    * playwright: 31%


## LEGAL & LICENSES

- move to EULA for all closed-source packages (cli)
- add EULA to docker container
- add cookie policy
- fix the "DPA" wording to be generic
- ship licenses from used third parties?

## Landing Page

Landing page should explain that Flakiness.io is 4 things:

1. Open Source Test Report Format
    * Works for any number of tests
    * Fully open-source specification
    * Language & Framework agnostic
Image: Report format schema/example or code snippet

2. Open-Source Adaptors
    * Native Playwright Test reporter
    * Native PyTest reporter
    * Easy to build custom adaptors
Image: Framework logos (Playwright, Pytest) or code example

3. Interactive Report Viewer (PWA)
    * Progressive Web App
    * Hosted on report.flakiness.io
    * Works offline, installable
Image: Viewer interface screenshot

4. Test Analytics Platform
    * Upload reports for automatic analysis
    * Regression and flakiness detection
    * Historical trends and insights
Image: Analytics dashboard screenshot

## Reuploaders

- fix `webkit_uploader` errors

## Report Viewer

- The local report viewer is local now, and even has Report Provider.
  Can I unify report provider to satisfy both local and cloud runs?
  Also, deal with commits?
- The page-run operates on Git.Commit types!
  Should be just commits instead!
  Also, should local report also produce `commits.json` - a list of local commits?
  We need to somehow find a commit to build off history.
- report.flakiness.io: should it use flakiness.io/api as a backend? If so, how? How do I configure SPA with a backend, and deployment time?

## Platform

- delete reports! sometimes i upload faulty reports and i want them to be deleted.
- why is commits history so SLOW to load? For kotlin? perf tests don't capture this.
- add perf tests with webkit data?
- show "Acceptable Flakiness Rate" and "regression window days" on tests report,
  commit report, PR report and History.
- reach out to some open source projects to give them early access?
- Calendar should have an option for Sunday-first of Monday-first week.
- Give some love to custom timelines editor?
- Loading spinner for commits history
- Overview tab in project:
    * Tests
        * # of tests
        * # of regressions
        * flakiness ratio over the week
    * Durations
        * top 5 slowest tests
        * test durations by buckets
    * Trends
        * top 5 steepest trend fastups
        * top 5 steepest trend downs
    * Environments
- SUPPORT UPLOAD STORMS!
  Do NOT re-read and re-write report-index-cache constantly!
    * Create a table with reports to-be-processed
    * This table:
        * project Id
        * run Id
        * status: started, uploaded
        * url
        * commitId
    * The upload worker then
        * fetches all uploaded runs for a given project Id
        * reads them one-by-one, aggregates them, etc

Artem's feedback on DOCUMENTATION:

- Flakiness Score for test: how flaky is the test? This will help to prioritize
  engineering effort: "What would be more impactful?"

Artem's feedback on PRODUCT:
- Browser Navigation: in-page navigation should not add to browser history, instead,
  it should overwrite last entry?
- Status of the test should PROMINENTLY SHOW that it leads to the last run!
    * The click on the test should lead to the last test run
    * Click on the chart should lead to test history
- The test-wise history is confusing: these are durations?! Artem was sure
  these are Test Runs count per day. He didn't even know that these are the
  "status & duration of the last test run in that day".
- Runs page:
    * Show run's status
    * Show stats
    * Show commit and commit's branch
    * Header with stats
    * 4 run statuses:


TODO:
- the initial markdown for empty project should show `npx flakiness link`
- make sure report endpoints are separate from database endpoints.

Renal feedback:
2. коммиты не открываются, это наверное ожидаемо (но может показаться что
   что-то сломано)
4. Какое-нибудь короткое видеодемо мб тоже прикол
5. А пермишены нельзя ослабить? а то Act on your behalf звучит страшно

Max feedback:
9. OIDC should not require you to set a token anymore, should be easy to implement imo.

Egor Feedback:
2. Не понял есть это или нет, но хочется еще сортировать тесты так, чтобы те,
   которые изменили статус после каммита были сверху (либо поломались, либо
   стали долгими)
3. Может не хватает мини туториалов, а может гайда по инсталяции и хватит.

TODO:
- delete execution-history component?
- Orchestration?
- on flakiness.io, I'd like to have report.flakiness.io for local report. Is this possible?
- on the landing page, show the "Reports processed" or "test runs processed"
  counter. We do have many test runs coming from kotlin + flakiness.
- show trend on # of test runs - this can explain why total time on timelines grows.
- speedup branch filtering (takes too long for Kotlin)
- branches page
- commit report
- page-run: show "other runs" in the commit
- archive report?
- For local report, pick history somehow?
- add validation in the report that all test names inside a file are different. Or should I?
- device auth
    * cache device auth table?
* do NOT expose `bench` in mitata reporter; make sure all
  test names are UNIQUE.
* use B.run() in mitata reporter instead of mitata.run().
  This way we'll be able to accurately match runs with trials.
* timelines editor should be nicer
* how to protect from too many timelines? This might slow down
  EVERYTHING.
* Should queue poll less often, but rely on xNotify to awake workers?
* commitStats MUST NOT send all runStats! Must be paged instead?
* index re-build: can i rebuild backwards? I want latest reports first, and then history
* test index: shall I include test suites? The test -> testSuite relation
  is not trivial
* report results: pie chart with results
- Issue: The same user can start unlimited number of Free Trials.
  * In stripe, we can set metadata for "customer" objects. Can we do this to
    avoid the second free trial for the same customer?
- hard limits for data uploads have to be configured?
- Polyrepo support!
- configure regression look-back in project settings ??

=== Observability ===

Right now, we observe deployment manually :) However, we should:
- monitor average time a job is queued / is executed
- setup prometheus alerts when some metrics go off (i.e. queued time is becoming
  more than 1 hour)

=== Server ===

DDoS'ing the server is easy now. We should:
* limit requests per person
* only single computation request per user should be computed at the same
  time

---

Usecases:
- local report
- shards: automatically merged
- cron jobs: report shows last results
- test history

TODO:
- trends page
- show test error (if any) when hovering over test status

Q:
- the ensure-all-reports-are-in-index job might conflict with data retention;
  we should not schedule reports to addition if they don't satisfy data retention policy.

Flakiness.io: Trend & Regression analytics for tests.

- Rich HTML Reports (feature-parity with Playwright HTML report)
- Trends & Regressions
- Flexible Programmatic Environment configuration
- Local Report

Flakiness.io features:

=== Target 1: SaaS Beta Release

- Support OIDC connect for github actions / gitlab actions (as max recommended)
  * https://docs.github.com/en/actions/security-for-github-actions/security-hardening-your-deployments/about-security-hardening-with-openid-connect

- When app installation gets removed, we should show a proper notification
  * right now, it is "something went wrong"

- Deployment script for cr.flakiness.io
  * otherwise we will forget how to do deploy it.

